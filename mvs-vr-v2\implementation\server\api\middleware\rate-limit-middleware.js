/**
 * Rate Limiting Middleware
 *
 * This middleware provides advanced rate limiting for the API Gateway.
 * It includes different rate limits for different endpoints, user/API key-based
 * rate limiting, progressive penalties for abuse, and Redis-based storage.
 */

const rateLimit = require('express-rate-limit');
const { logger } = require('../../shared/utils/logger');

// For now, we'll use the default memory store instead of Redis
// This can be upgraded to Redis later when Redis is properly configured
// const RedisStore = require('rate-limit-redis');
// const { redis } = require('./auth-middleware');

// Default rate limit configuration
const DEFAULT_RATE_LIMIT = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    status: 429,
    error: 'Too many requests, please try again later.',
  },
};

// For now, we'll use the default memory store
// TODO: Implement Redis store when Redis is properly configured
// const redisStore = new RedisStore({
//   sendCommand: (...args) => redis.call(...args),
//   prefix: 'rate-limit:',
//   expiry: Math.ceil(DEFAULT_RATE_LIMIT.windowMs / 1000) + 10,
// });

/**
 * Create a rate limiter with custom options
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const createRateLimiter = (options = {}) => {
  const config = {
    ...DEFAULT_RATE_LIMIT,
    ...options,
    // Using default memory store for now
    // store: redisStore,
    // Skip rate limiting for trusted IPs
    skip: req => {
      const trustedIps = process.env.TRUSTED_IPS ? process.env.TRUSTED_IPS.split(',') : [];
      return trustedIps.includes(req.ip);
    },
    // Use a custom key generator to include user ID or API key if available
    keyGenerator: req => {
      // If user is authenticated, include user ID in the key
      if (req.user && req.user.id) {
        return `${req.ip}:${req.user.id}:${req.originalUrl}`;
      }

      // If API key is used, include API key ID in the key
      if (req.apiKey && req.apiKey.id) {
        return `${req.ip}:apikey:${req.apiKey.id}:${req.originalUrl}`;
      }

      // Default to IP-based rate limiting
      return `${req.ip}:${req.originalUrl}`;
    },
    // Handle rate limit exceeded
    handler: (req, res, next, options) => {
      const clientIp = req.ip || req.connection.remoteAddress;

      // Log rate limit exceeded
      logger.warn(`Rate limit exceeded: ${clientIp}`, {
        ip: clientIp,
        path: req.originalUrl,
        method: req.method,
        userId: req.user?.id,
        apiKeyId: req.apiKey?.id,
      });

      // Track abuse for progressive penalties
      trackRateLimitAbuse(clientIp, req.originalUrl);

      // Send response
      res.status(options.statusCode).json(options.message);
    },
  };

  return rateLimit(config);
};

/**
 * Track rate limit abuse for progressive penalties
 * @param {string} ip - Client IP address
 * @param {string} path - Request path
 * @param {Object} req - Express request object (optional)
 */
async function trackRateLimitAbuse(ip, path, req = null) {
  try {
    // For now, just log the abuse since Redis is not configured
    // TODO: Implement Redis-based abuse tracking when Redis is available

    const abuseData = {
      timestamp: Date.now(),
      ip,
      path,
      method: req?.method,
      userId: req?.user?.id,
      apiKeyId: req?.apiKey?.id,
    };

    logger.warn('Rate limit abuse detected (memory-only tracking)', abuseData);

    // TODO: Implement proper abuse tracking with Redis
    // const count = await redis.hincrby(abuseKey, 'count', 1);
    // ... rest of Redis implementation
  } catch (error) {
    logger.error('Error tracking rate limit abuse:', error);
  }
}

/**
 * Check if IP is blocked due to abuse
 * @param {string} ip - Client IP address
 * @returns {Promise<boolean>} True if IP is blocked
 */
async function isIpBlocked(ip) {
  try {
    // For now, no IPs are blocked since Redis is not configured
    // TODO: Implement Redis-based IP blocking when Redis is available
    return false;
  } catch (error) {
    logger.error('Error checking if IP is blocked:', error);
    return false;
  }
}

// Create different rate limiters for different endpoints
const apiLimiter = createRateLimiter();

const authLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour for auth endpoints
  message: {
    status: 429,
    error: 'Too many authentication attempts, please try again later.',
  },
});

const userLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 requests per hour for user endpoints
  message: {
    status: 429,
    error: 'Too many user-related requests, please try again later.',
  },
});

const assetLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // 50 requests per 5 minutes for asset endpoints
  message: {
    status: 429,
    error: 'Too many asset-related requests, please try again later.',
  },
});

module.exports = {
  apiLimiter,
  authLimiter,
  userLimiter,
  assetLimiter,
  createRateLimiter,
  isIpBlocked,
  trackRateLimitAbuse,
};
