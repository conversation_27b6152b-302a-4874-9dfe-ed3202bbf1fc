/**
 * Simple MVS-VR Server
 * 
 * A simplified version of the server for testing basic functionality
 */

import dotenv from 'dotenv';
import express from 'express';
import { logger } from './shared/utils/logger';

// Load environment variables
dotenv.config();

// Get port from environment variables
const port = parseInt(process.env.PORT || '3000', 10);

// Create Express app
const app = express();

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'MVS-VR Server is running!',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    message: 'API is working!',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Server error', { error: error.message, stack: error.stack });
  res.status(500).json({
    error: 'Internal Server Error',
    message: error.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`
  });
});

// Start server
async function startSimpleServer() {
  try {
    const server = app.listen(port, () => {
      logger.info(`Simple MVS-VR server started on port ${port}`);
      logger.info('Available endpoints:');
      logger.info('- GET / - Server status');
      logger.info('- GET /health - Health check');
      logger.info('- GET /api/test - API test');
    });

    return server;
  } catch (error) {
    logger.error('Failed to start simple server', { error });
    throw error;
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startSimpleServer().catch(error => {
    logger.error('Simple server startup failed', { error });
    process.exit(1);
  });
}

// Handle process termination
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export { startSimpleServer };
