/**
 * Metrics Module
 * 
 * This module exports commonly used metrics for the MVS-VR server.
 * It provides a centralized way to access metrics across different services.
 */

const promClient = require('prom-client');

// Create a registry for metrics
const register = new promClient.Registry();

// Asset compression metrics
const assetCompressionDuration = new promClient.Histogram({
  name: 'mvs_vr_asset_compression_duration_seconds',
  help: 'Time taken to compress assets in seconds',
  labelNames: ['algorithm', 'asset_type', 'compression_level'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
});

const assetCompressionRatio = new promClient.Gauge({
  name: 'mvs_vr_asset_compression_ratio',
  help: 'Compression ratio achieved (original_size / compressed_size)',
  labelNames: ['algorithm', 'asset_type', 'compression_level']
});

const assetCompressionTotal = new promClient.Counter({
  name: 'mvs_vr_asset_compression_total',
  help: 'Total number of assets compressed',
  labelNames: ['algorithm', 'asset_type', 'compression_level', 'status']
});

// Asset processing metrics
const assetProcessingDuration = new promClient.Histogram({
  name: 'mvs_vr_asset_processing_duration_seconds',
  help: 'Time taken to process assets in seconds',
  labelNames: ['operation', 'asset_type'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60, 120]
});

const assetProcessingTotal = new promClient.Counter({
  name: 'mvs_vr_asset_processing_total',
  help: 'Total number of assets processed',
  labelNames: ['operation', 'asset_type', 'status']
});

const assetProcessingErrors = new promClient.Counter({
  name: 'mvs_vr_asset_processing_errors_total',
  help: 'Total number of asset processing errors',
  labelNames: ['operation', 'asset_type', 'error_type']
});

// HTTP request metrics
const httpRequestsTotal = new promClient.Counter({
  name: 'mvs_vr_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status']
});

const httpRequestDuration = new promClient.Histogram({
  name: 'mvs_vr_http_request_duration_seconds',
  help: 'HTTP request duration in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
});

// Database metrics
const dbQueryDuration = new promClient.Histogram({
  name: 'mvs_vr_db_query_duration_seconds',
  help: 'Database query duration in seconds',
  labelNames: ['query_type', 'table'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2]
});

const dbTransactions = new promClient.Counter({
  name: 'mvs_vr_db_transactions_total',
  help: 'Total number of database transactions',
  labelNames: ['type', 'status']
});

// Cache metrics
const cacheHits = new promClient.Counter({
  name: 'mvs_vr_cache_hits_total',
  help: 'Total number of cache hits',
  labelNames: ['cache_type', 'key_pattern']
});

const cacheMisses = new promClient.Counter({
  name: 'mvs_vr_cache_misses_total',
  help: 'Total number of cache misses',
  labelNames: ['cache_type', 'key_pattern']
});

const cacheOperationDuration = new promClient.Histogram({
  name: 'mvs_vr_cache_operation_duration_seconds',
  help: 'Cache operation duration in seconds',
  labelNames: ['operation', 'cache_type'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
});

// Business metrics
const activeUsers = new promClient.Gauge({
  name: 'mvs_vr_active_users',
  help: 'Number of active users',
  labelNames: ['role']
});

const assetUploads = new promClient.Counter({
  name: 'mvs_vr_asset_uploads_total',
  help: 'Total number of asset uploads',
  labelNames: ['asset_type', 'vendor']
});

const assetDownloads = new promClient.Counter({
  name: 'mvs_vr_asset_downloads_total',
  help: 'Total number of asset downloads',
  labelNames: ['asset_type', 'client']
});

const showroomVisits = new promClient.Counter({
  name: 'mvs_vr_showroom_visits_total',
  help: 'Total number of showroom visits',
  labelNames: ['showroom_id', 'client']
});

// Error metrics
const errorRate = new promClient.Counter({
  name: 'mvs_vr_errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'code', 'service']
});

// Register all metrics
const metrics = {
  assetCompressionDuration,
  assetCompressionRatio,
  assetCompressionTotal,
  assetProcessingDuration,
  assetProcessingTotal,
  assetProcessingErrors,
  httpRequestsTotal,
  httpRequestDuration,
  dbQueryDuration,
  dbTransactions,
  cacheHits,
  cacheMisses,
  cacheOperationDuration,
  activeUsers,
  assetUploads,
  assetDownloads,
  showroomVisits,
  errorRate
};

// Register all metrics with the registry
Object.values(metrics).forEach(metric => {
  try {
    register.registerMetric(metric);
  } catch (error) {
    // Metric might already be registered, ignore
  }
});

module.exports = {
  ...metrics,
  register
};
