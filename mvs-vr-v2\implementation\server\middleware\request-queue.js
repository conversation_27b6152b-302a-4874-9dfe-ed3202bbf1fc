/**
 * Request Queue Middleware
 *
 * This middleware implements a request queuing system for high-load scenarios.
 * It helps prevent server overload by limiting the number of concurrent requests
 * and queuing excess requests.
 */

const { Gauge, Counter } = require('prom-client');
const { logger } = require('../shared/utils/logger.ts');

// Create metrics
const requestsActive = new Gauge({
  name: 'request_queue_active',
  help: 'Number of active requests being processed',
  labelNames: ['queue'],
});

const requestsQueued = new Gauge({
  name: 'request_queue_queued',
  help: 'Number of requests waiting in the queue',
  labelNames: ['queue'],
});

const requestsRejected = new Counter({
  name: 'request_queue_rejected_total',
  help: 'Total number of requests rejected due to queue overflow',
  labelNames: ['queue'],
});

const requestsProcessed = new Counter({
  name: 'request_queue_processed_total',
  help: 'Total number of requests processed',
  labelNames: ['queue'],
});

const requestQueueTime = new Counter({
  name: 'request_queue_time_seconds',
  help: 'Time spent in queue in seconds',
  labelNames: ['queue'],
});

const requestProcessingTime = new Counter({
  name: 'request_queue_processing_time_seconds',
  help: 'Time spent processing requests in seconds',
  labelNames: ['queue'],
});

/**
 * Request queue class
 */
class RequestQueue {
  /**
   * Constructor
   * @param {Object} options - Queue options
   */
  constructor(options = {}) {
    const {
      name = 'default',
      concurrency = parseInt(process.env.REQUEST_QUEUE_CONCURRENCY || '100', 10),
      maxQueueSize = parseInt(process.env.REQUEST_QUEUE_MAX_SIZE || '1000', 10),
      timeout = parseInt(process.env.REQUEST_QUEUE_TIMEOUT || '30000', 10),
      priorityFn = null,
    } = options;

    this.name = name;
    this.concurrency = concurrency;
    this.maxQueueSize = maxQueueSize;
    this.timeout = timeout;
    this.priorityFn = priorityFn;

    this.active = 0;
    this.queue = [];

    // Initialize metrics
    requestsActive.set({ queue: this.name }, 0);
    requestsQueued.set({ queue: this.name }, 0);

    logger.info(`Created request queue "${name}"`, {
      concurrency,
      maxQueueSize,
      timeout,
    });
  }

  /**
   * Add a request to the queue
   * @param {Function} fn - Function to execute
   * @param {Object} context - Request context
   * @returns {Promise} Promise that resolves when the request is processed
   */
  async add(fn, context = {}) {
    // If we have capacity, process immediately
    if (this.active < this.concurrency) {
      return this.process(fn, context);
    }

    // If queue is full, reject
    if (this.queue.length >= this.maxQueueSize) {
      requestsRejected.inc({ queue: this.name });

      logger.warn(`Request queue "${this.name}" overflow`, {
        active: this.active,
        queued: this.queue.length,
        maxQueueSize: this.maxQueueSize,
      });

      throw new Error(`Request queue "${this.name}" overflow`);
    }

    // Add to queue
    return new Promise((resolve, reject) => {
      const queuedAt = Date.now();

      const timeoutId = setTimeout(() => {
        // Remove from queue
        const index = this.queue.findIndex(item => item.id === timeoutId);

        if (index !== -1) {
          this.queue.splice(index, 1);
          requestsQueued.set({ queue: this.name }, this.queue.length);
        }

        requestsRejected.inc({ queue: this.name });

        logger.warn(`Request in queue "${this.name}" timed out`, {
          active: this.active,
          queued: this.queue.length,
          timeout: this.timeout,
        });

        reject(new Error(`Request in queue "${this.name}" timed out after ${this.timeout}ms`));
      }, this.timeout);

      // Add to queue
      this.queue.push({
        id: timeoutId,
        fn,
        context,
        resolve,
        reject,
        queuedAt,
        priority: this.priorityFn ? this.priorityFn(context) : 0,
      });

      // Sort queue by priority (higher first)
      if (this.priorityFn) {
        this.queue.sort((a, b) => b.priority - a.priority);
      }

      requestsQueued.set({ queue: this.name }, this.queue.length);

      logger.debug(`Request added to queue "${this.name}"`, {
        active: this.active,
        queued: this.queue.length,
        priority: this.priorityFn ? this.priorityFn(context) : 0,
      });
    });
  }

  /**
   * Process a request
   * @param {Function} fn - Function to execute
   * @param {Object} context - Request context
   * @returns {Promise} Promise that resolves with the result of the function
   */
  async process(fn, context = {}) {
    this.active++;
    requestsActive.set({ queue: this.name }, this.active);

    const startTime = Date.now();

    try {
      const result = await fn();

      requestsProcessed.inc({ queue: this.name });
      requestProcessingTime.inc({ queue: this.name }, (Date.now() - startTime) / 1000);

      return result;
    } finally {
      this.active--;
      requestsActive.set({ queue: this.name }, this.active);

      // Process next request in queue
      this.processNext();
    }
  }

  /**
   * Process next request in queue
   */
  processNext() {
    if (this.queue.length === 0 || this.active >= this.concurrency) {
      return;
    }

    const { id, fn, context, resolve, reject, queuedAt } = this.queue.shift();
    requestsQueued.set({ queue: this.name }, this.queue.length);

    // Clear timeout
    clearTimeout(id);

    // Calculate queue time
    const queueTime = (Date.now() - queuedAt) / 1000;
    requestQueueTime.inc({ queue: this.name }, queueTime);

    logger.debug(`Request dequeued from "${this.name}"`, {
      active: this.active,
      queued: this.queue.length,
      queueTime,
    });

    // Process request
    this.process(fn, context).then(resolve).catch(reject);
  }

  /**
   * Get queue statistics
   * @returns {Object} Queue statistics
   */
  getStats() {
    return {
      name: this.name,
      active: this.active,
      queued: this.queue.length,
      concurrency: this.concurrency,
      maxQueueSize: this.maxQueueSize,
      timeout: this.timeout,
    };
  }
}

/**
 * Create request queue middleware
 * @param {Object} options - Queue options
 * @returns {Function} Express middleware
 */
function requestQueue(options = {}) {
  const queue = new RequestQueue(options);

  return (req, res, next) => {
    // Skip health check endpoints
    if (req.path === '/health' || req.path === '/healthz') {
      return next();
    }

    // Create context for priority function
    const context = {
      path: req.path,
      method: req.method,
      ip: req.ip,
      user: req.user,
      apiKey: req.apiKey,
    };

    // Add request to queue
    queue
      .add(
        () =>
          new Promise(resolve => {
            // Store original end method
            const originalEnd = res.end;

            // Override end method
            res.end = function (...args) {
              // Restore original end method
              res.end = originalEnd;

              // Call original end method
              originalEnd.apply(res, args);

              // Resolve promise
              resolve();
            };

            // Continue request processing
            next();
          }),
        context,
      )
      .catch(error => {
        // Handle queue errors
        if (error.message.includes('overflow')) {
          res.status(503).json({
            error: 'ServiceUnavailable',
            message: 'Server is too busy, please try again later',
            details: {
              retryAfter: 10,
            },
          });

          // Add Retry-After header
          res.set('Retry-After', '10');
        } else if (error.message.includes('timed out')) {
          res.status(503).json({
            error: 'ServiceUnavailable',
            message: 'Request processing timed out, please try again later',
            details: {
              retryAfter: 30,
            },
          });

          // Add Retry-After header
          res.set('Retry-After', '30');
        } else {
          res.status(500).json({
            error: 'InternalServerError',
            message: 'An unexpected error occurred',
            details: null,
          });
        }
      });
  };
}

module.exports = requestQueue;
